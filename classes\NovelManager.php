<?php
/**
 * Novel Manager Class
 * Handles novel operations and database interactions
 */

class NovelManager {
    private $db;
    private $translationService;
    private $chapterChunker;

    public function __construct() {
        try {
            $this->db = Database::getInstance();
            $this->translationService = new TranslationService();
            $this->chapterChunker = new ChapterChunker();
            error_log("NovelManager: Successfully initialized all services");
        } catch (Exception $e) {
            error_log("NovelManager: Failed to initialize services: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Preview novel information from URL
     */
    public function previewNovel(string $url): array {
        $platform = validateUrl($url);
        if (!$platform) {
            throw new Exception("Unsupported URL or invalid format");
        }
        
        $crawler = $this->getCrawler($platform);
        
        try {
            // Get novel info
            $novelInfo = $crawler->getNovelInfo($url);

            // Log extracted data for debugging
            error_log("NovelManager: Extracted novel info - " . json_encode([
                'title' => $novelInfo['original_title'] ?? 'NULL',
                'author' => $novelInfo['author'] ?? 'NULL',
                'synopsis_length' => strlen($novelInfo['original_synopsis'] ?? ''),
                'publication_date' => $novelInfo['publication_date'] ?? 'NULL'
            ]));

            // Get chapter list
            $chapters = $crawler->getChapterList($url);

            // Translate title and synopsis for preview
            $titleTranslation = $this->translationService->translateText(
                $novelInfo['original_title'],
                DEFAULT_TARGET_LANGUAGE
            );

            $synopsisTranslation = $this->translationService->translateText(
                $novelInfo['original_synopsis'],
                DEFAULT_TARGET_LANGUAGE
            );

            return [
                'success' => true,
                'novel_info' => array_merge($novelInfo, [
                    'translated_title' => $titleTranslation['success'] ? $titleTranslation['translated_text'] : null,
                    'translated_synopsis' => $synopsisTranslation['success'] ? $synopsisTranslation['translated_text'] : null
                ]),
                'chapters' => $chapters,
                'total_chapters' => count($chapters)
            ];

        } catch (Exception $e) {
            error_log("NovelManager: Preview error - " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Save novel to database
     */
    public function saveNovel(array $novelData): array {
        try {
            $this->db->beginTransaction();

            // Log the data being saved for debugging
            error_log("NovelManager: Saving novel data - " . json_encode([
                'url' => $novelData['url'] ?? 'NULL',
                'author' => $novelData['author'] ?? 'NULL',
                'synopsis_length' => strlen($novelData['original_synopsis'] ?? ''),
                'publication_date' => $novelData['publication_date'] ?? 'NULL'
            ]));

            // Check if novel already exists
            $existing = $this->db->fetchOne(
                "SELECT id FROM novels WHERE url = ?",
                [$novelData['url']]
            );

            if ($existing) {
                throw new Exception("Novel already exists in database");
            }

            // Prepare data for insertion
            $insertData = [
                'url' => $novelData['url'],
                'platform' => $novelData['platform'],
                'original_title' => $novelData['original_title'],
                'translated_title' => $novelData['translated_title'] ?? null,
                'author' => $novelData['author'] ?? null,
                'publication_date' => $novelData['publication_date'] ?? null,
                'total_chapters' => $novelData['total_chapters'] ?? 0,
                'original_synopsis' => $novelData['original_synopsis'] ?? null,
                'translated_synopsis' => $novelData['translated_synopsis'] ?? null
            ];

            // Log what's actually being inserted
            error_log("NovelManager: Insert data - " . json_encode($insertData));

            // Insert novel
            $novelId = $this->db->insert('novels', $insertData);
            
            // Insert chapters if provided
            if (isset($novelData['chapters']) && is_array($novelData['chapters'])) {
                foreach ($novelData['chapters'] as $chapter) {
                    $this->db->insert('chapters', [
                        'novel_id' => $novelId,
                        'chapter_number' => $chapter['chapter_number'],
                        'chapter_url' => $chapter['chapter_url'],
                        'original_title' => processTitleText($chapter['original_title'] ?? '')
                    ]);
                }
            }
            
            $this->db->commit();
            
            return [
                'success' => true,
                'novel_id' => $novelId,
                'message' => 'Novel saved successfully'
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get saved novels
     */
    public function getSavedNovels(int $page = 1, int $limit = 20): array {
        try {
            $offset = ($page - 1) * $limit;

            $novels = $this->db->fetchAll(
                "SELECT n.*,
                        COUNT(c.id) as saved_chapters,
                        COUNT(CASE WHEN c.translation_status = 'completed' THEN 1 END) as translated_chapters
                 FROM novels n
                 LEFT JOIN chapters c ON n.id = c.novel_id
                 GROUP BY n.id
                 ORDER BY n.created_at DESC
                 LIMIT ? OFFSET ?",
                [$limit, $offset]
            );

            // Ensure novels is always an array
            if (!is_array($novels)) {
                error_log("NovelManager: getSavedNovels returned non-array: " . gettype($novels));
                $novels = [];
            }

            $totalResult = $this->db->fetchOne("SELECT COUNT(*) as count FROM novels");
            $total = $totalResult ? (int)$totalResult['count'] : 0;

            $result = [
                'novels' => $novels,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($total / $limit),
                    'total_novels' => $total,
                    'per_page' => $limit
                ]
            ];

            error_log("NovelManager: getSavedNovels returning " . count($novels) . " novels");
            return $result;

        } catch (Exception $e) {
            error_log("NovelManager: Error in getSavedNovels: " . $e->getMessage());
            return [
                'novels' => [],
                'pagination' => [
                    'current_page' => 1,
                    'total_pages' => 0,
                    'total_novels' => 0,
                    'per_page' => $limit
                ]
            ];
        }
    }
    
    /**
     * Get novel details with chapters (with pagination and search)
     */
    public function getNovelDetails(int $novelId, array $options = []): array {
        $novel = $this->db->fetchOne(
            "SELECT * FROM novels WHERE id = ?",
            [$novelId]
        );

        if (!$novel) {
            throw new Exception("Novel not found");
        }

        // Build chapter query with search and pagination
        $whereClause = "c.novel_id = ?";
        $params = [$novelId];

        // Add search filter if provided
        if (!empty($options['search'])) {
            $searchTerm = '%' . $options['search'] . '%';
            $whereClause .= " AND (c.original_title LIKE ? OR c.translated_title LIKE ? OR c.chapter_number LIKE ?)";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        // Add chapter range filter if provided
        if (!empty($options['from_chapter'])) {
            $whereClause .= " AND c.chapter_number >= ?";
            $params[] = $options['from_chapter'];
        }

        if (!empty($options['to_chapter'])) {
            $whereClause .= " AND c.chapter_number <= ?";
            $params[] = $options['to_chapter'];
        }

        // Get total count for pagination
        $totalChapters = $this->db->fetchOne(
            "SELECT COUNT(*) as total FROM chapters c WHERE {$whereClause}",
            $params
        )['total'];

        // Add pagination if provided
        $limit = $options['limit'] ?? null;
        $offset = 0;
        if ($limit && isset($options['page'])) {
            $page = max(1, (int)$options['page']);
            $offset = ($page - 1) * $limit;
        }

        $orderBy = "ORDER BY c.chapter_number";
        $limitClause = $limit ? "LIMIT {$limit} OFFSET {$offset}" : "";

        $chapters = $this->db->fetchAll(
            "SELECT c.*,
                    CASE WHEN cc.chapter_id IS NOT NULL THEN 1 ELSE 0 END as has_chunks,
                    COUNT(cc.id) as chunk_count
             FROM chapters c
             LEFT JOIN chapter_chunks cc ON c.id = cc.chapter_id
             WHERE {$whereClause}
             GROUP BY c.id
             {$orderBy}
             {$limitClause}",
            $params
        );

        $nameDictionary = $this->db->fetchAll(
            "SELECT * FROM name_dictionary WHERE novel_id = ? ORDER BY frequency DESC",
            [$novelId]
        );

        $result = [
            'novel' => $novel,
            'chapters' => $chapters,
            'name_dictionary' => $nameDictionary
        ];

        // Add pagination info if applicable
        if ($limit) {
            $totalPages = ceil($totalChapters / $limit);
            $currentPage = isset($options['page']) ? max(1, (int)$options['page']) : 1;

            $result['pagination'] = [
                'current_page' => $currentPage,
                'total_pages' => $totalPages,
                'total_chapters' => $totalChapters,
                'per_page' => $limit,
                'has_next' => $currentPage < $totalPages,
                'has_prev' => $currentPage > 1
            ];
        }

        return $result;
    }
    
    /**
     * Save chapter content
     */
    public function saveChapter(int $novelId, int $chapterNumber): array {
        try {
            // Validate input parameters
            if ($novelId <= 0) {
                throw new Exception("Invalid novel ID");
            }
            if ($chapterNumber <= 0) {
                throw new Exception("Invalid chapter number");
            }

            // Get chapter info
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if (!$chapter) {
                throw new Exception("Chapter not found");
            }

            if ($chapter['original_content']) {
                return [
                    'success' => true,
                    'message' => 'Chapter already saved',
                    'chapter' => $chapter
                ];
            }

            // Get novel info for platform
            $novel = $this->db->fetchOne(
                "SELECT platform FROM novels WHERE id = ?",
                [$novelId]
            );

            if (!$novel) {
                throw new Exception("Novel not found");
            }

            if (empty($novel['platform'])) {
                throw new Exception("Novel platform not specified");
            }

            // Initialize crawler
            try {
                $crawler = $this->getCrawler($novel['platform']);
            } catch (Exception $e) {
                throw new Exception("Failed to initialize crawler: " . $e->getMessage());
            }

            // Fetch chapter content with enhanced error handling
            try {
                error_log("NovelManager: About to fetch chapter content from: " . $chapter['chapter_url']);
                $chapterContent = $crawler->getChapterContent($chapter['chapter_url']);
                error_log("NovelManager: Chapter content fetched successfully, length: " . strlen($chapterContent['original_content'] ?? ''));
            } catch (Exception $e) {
                error_log("NovelManager: Failed to fetch chapter content: " . $e->getMessage());
                throw new Exception("Failed to fetch chapter content: " . $e->getMessage());
            }

            if (empty($chapterContent['original_content'])) {
                error_log("NovelManager: No content retrieved from chapter URL: " . $chapter['chapter_url']);
                throw new Exception("No content retrieved from chapter URL");
            }

            // Log content preview for debugging
            $contentPreview = substr($chapterContent['original_content'], 0, 200);
            error_log("NovelManager: Content preview: " . $contentPreview);

            // Process furigana if detected
            try {
                $furiganaService = new FuriganaService();
            } catch (Exception $e) {
                // If furigana service fails, continue without it
                error_log("Warning: Failed to initialize FuriganaService: " . $e->getMessage());
                $furiganaService = null;
            }

            // Calculate content statistics with error handling
            try {
                error_log("NovelManager: About to calculate content stats for content length: " . strlen($chapterContent['original_content']));
                $contentStats = $this->chapterChunker->calculateContentStats($chapterContent['original_content']);
                error_log("NovelManager: Content stats calculated successfully");
            } catch (Exception $e) {
                error_log("NovelManager: Error calculating content stats: " . $e->getMessage());
                // Provide fallback content stats
                $contentStats = [
                    'character_count' => strlen($chapterContent['original_content']),
                    'word_count' => str_word_count($chapterContent['original_content']),
                    'needs_chunking' => false,
                    'estimated_chunks' => 1,
                    'complexity' => ['score' => 1.0, 'factors' => []]
                ];
            }

            // Log content analysis for debugging
            error_log("NovelManager: Chapter content analysis - Length: {$contentStats['character_count']}, Needs chunking: " . ($contentStats['needs_chunking'] ? 'YES' : 'NO') . ", Estimated chunks: {$contentStats['estimated_chunks']}");

            $updateData = [
                'original_content' => $chapterContent['original_content'],
                'word_count' => $contentStats['word_count']
            ];

            // Check if content contains furigana markup and service is available
            if ($furiganaService && preg_match('/\{[^|]+\|[^}]+\}/', $chapterContent['original_content'])) {
                $updateData['original_content_with_furigana'] = $chapterContent['original_content'];
                $updateData['furigana_processing_status'] = 'detected';

                try {
                    // Process furigana data
                    $furiganaResult = $furiganaService->processChapterFurigana(
                        $chapter['id'],
                        $chapterContent['original_content']
                    );

                    if ($furiganaResult['success']) {
                        $updateData['furigana_count'] = $furiganaResult['furigana_count'];
                        $updateData['furigana_processing_status'] = 'processed';
                    }

                    // Clean content for storage (remove furigana markup for base content)
                    $updateData['original_content'] = $furiganaService->removeFurigana($chapterContent['original_content']);
                } catch (Exception $e) {
                    // If furigana processing fails, log error but continue
                    error_log("Warning: Furigana processing failed: " . $e->getMessage());
                    $updateData['furigana_processing_status'] = 'error';
                }
            }

            // Update chapter with content
            try {
                $rowsUpdated = $this->db->update('chapters', $updateData, 'id = ?', [$chapter['id']]);
                if ($rowsUpdated === 0) {
                    throw new Exception("Failed to update chapter in database");
                }
            } catch (Exception $e) {
                throw new Exception("Database update failed: " . $e->getMessage());
            }

            // Check if chapter needs chunking and create chunks if necessary
            $chunkingResult = null;
            if ($contentStats['needs_chunking']) {
                error_log("NovelManager: Chapter needs chunking - creating chunks for chapter {$chapter['id']}");
                try {
                    $chunkingResult = $this->chapterChunker->splitChapter($chapter['id'], $updateData['original_content']);
                    if ($chunkingResult['success']) {
                        error_log("NovelManager: Chunking successful - created {$chunkingResult['chunks_created']} chunks");
                    } else {
                        error_log("NovelManager: Chapter chunking failed: " . $chunkingResult['error']);
                        // Don't fail the save operation if chunking fails - the chapter can still be saved
                        // Translation will fall back to regular method
                    }
                } catch (Exception $e) {
                    error_log("NovelManager: Chapter chunking exception: " . $e->getMessage());
                    // Don't fail the save operation if chunking fails
                    $chunkingResult = [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'chunks_created' => 0
                    ];
                }
            } else {
                error_log("NovelManager: Chapter does not need chunking - content length: {$contentStats['character_count']} chars");
            }

            return [
                'success' => true,
                'message' => 'Chapter saved successfully',
                'chapter' => array_merge($chapter, $chapterContent),
                'furigana_detected' => isset($updateData['original_content_with_furigana']),
                'furigana_count' => $updateData['furigana_count'] ?? 0,
                'content_stats' => $contentStats,
                'chunking_result' => $chunkingResult
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Translate chapter
     */
    public function translateChapter(int $novelId, int $chapterNumber, string $targetLanguage = 'en', array $context = []): array {
        try {
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if (!$chapter) {
                throw new Exception("Chapter not found");
            }

            if (!$chapter['original_content']) {
                throw new Exception("Chapter content not saved. Please save the chapter first.");
            }

            return $this->translationService->translateChapter($novelId, $chapter['id'], $targetLanguage, $context);

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update chapter translation content
     */
    public function updateChapterTranslation(int $novelId, int $chapterNumber, string $translatedContent): array {
        try {
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if (!$chapter) {
                throw new Exception("Chapter not found");
            }

            // Update chapter with new translation
            $updateData = [
                'translated_content' => $translatedContent,
                'translation_status' => 'completed',
                'translation_date' => date('Y-m-d H:i:s'),
                'word_count' => str_word_count($translatedContent)
            ];

            $updated = $this->db->update('chapters', $updateData, 'id = ?', [$chapter['id']]);

            if ($updated === 0) {
                throw new Exception("Failed to update chapter translation");
            }

            return [
                'success' => true,
                'message' => 'Chapter translation updated successfully',
                'chapter_id' => $chapter['id']
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update chapter title
     */
    public function updateChapterTitle(int $novelId, int $chapterNumber, string $translatedTitle): array {
        try {
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if (!$chapter) {
                throw new Exception("Chapter not found");
            }

            // Validate that the title is not empty
            if (empty(trim($translatedTitle))) {
                throw new Exception("Translated title cannot be empty");
            }

            // Update chapter with new translated title
            $updateData = [
                'translated_title' => trim($translatedTitle),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $updated = $this->db->update('chapters', $updateData, 'id = ?', [$chapter['id']]);

            if ($updated === 0) {
                throw new Exception("Failed to update chapter title");
            }

            // Return updated chapter data
            $updatedChapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE id = ?",
                [$chapter['id']]
            );

            return [
                'success' => true,
                'message' => 'Chapter title updated successfully',
                'chapter' => $updatedChapter
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update chapter original content manually
     */
    public function updateChapterOriginalContent(int $novelId, int $chapterNumber, string $originalContent): array {
        try {
            $this->db->beginTransaction();

            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if (!$chapter) {
                throw new Exception("Chapter not found");
            }

            // Validate that the content is not empty
            if (empty(trim($originalContent))) {
                throw new Exception("Original content cannot be empty");
            }

            // Store previous content for logging
            $previousContent = $chapter['original_content'];
            $previousLength = strlen($previousContent ?? '');
            $newLength = strlen($originalContent);

            // Process furigana if detected
            $furiganaService = null;
            try {
                $furiganaService = new FuriganaService();
            } catch (Exception $e) {
                error_log("Warning: Failed to initialize FuriganaService: " . $e->getMessage());
            }

            // Calculate content statistics
            try {
                $contentStats = $this->chapterChunker->calculateContentStats($originalContent);
            } catch (Exception $e) {
                error_log("Warning: Error calculating content stats: " . $e->getMessage());
                // Provide fallback content stats
                $contentStats = [
                    'character_count' => strlen($originalContent),
                    'word_count' => str_word_count($originalContent),
                    'needs_chunking' => false,
                    'estimated_chunks' => 1,
                    'complexity' => ['score' => 1.0, 'factors' => []]
                ];
            }

            $updateData = [
                'original_content' => trim($originalContent),
                'word_count' => $contentStats['word_count'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Check if content contains furigana markup and service is available
            if ($furiganaService && preg_match('/\{[^|]+\|[^}]+\}/', $originalContent)) {
                $updateData['original_content_with_furigana'] = $originalContent;
                $updateData['furigana_processing_status'] = 'detected';

                try {
                    // Process furigana data
                    $furiganaResult = $furiganaService->processChapterFurigana(
                        $chapter['id'],
                        $originalContent
                    );

                    if ($furiganaResult['success']) {
                        $updateData['furigana_count'] = $furiganaResult['furigana_count'];
                        $updateData['furigana_processing_status'] = 'processed';
                    }

                    // Clean content for storage (remove furigana markup for base content)
                    $updateData['original_content'] = $furiganaService->removeFurigana($originalContent);
                } catch (Exception $e) {
                    error_log("Warning: Furigana processing failed: " . $e->getMessage());
                    $updateData['furigana_processing_status'] = 'error';
                }
            } else {
                // Clear furigana data if no furigana markup detected
                $updateData['original_content_with_furigana'] = null;
                $updateData['furigana_count'] = 0;
                $updateData['furigana_processing_status'] = 'none';
            }

            // Update chapter with new content
            $updated = $this->db->update('chapters', $updateData, 'id = ?', [$chapter['id']]);

            if ($updated === 0) {
                throw new Exception("Failed to update chapter content");
            }

            // Clear existing chunks if any (they'll need to be regenerated)
            $this->db->query("DELETE FROM chapter_chunks WHERE chapter_id = ?", [$chapter['id']]);

            // Check if chapter needs chunking and create chunks if necessary
            $chunkingResult = null;
            if ($contentStats['needs_chunking']) {
                try {
                    $chunkingResult = $this->chapterChunker->splitChapter($chapter['id'], $updateData['original_content']);
                    if ($chunkingResult['success']) {
                        error_log("NovelManager: Manual edit chunking successful - created {$chunkingResult['chunks_created']} chunks");
                    }
                } catch (Exception $e) {
                    error_log("NovelManager: Manual edit chunking failed: " . $e->getMessage());
                    $chunkingResult = [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'chunks_created' => 0
                    ];
                }
            }

            // Log the edit action for audit purposes
            $logData = [
                'action' => 'manual_edit_original_content',
                'novel_id' => $novelId,
                'chapter_id' => $chapter['id'],
                'chapter_number' => $chapterNumber,
                'previous_length' => $previousLength,
                'new_length' => $newLength,
                'user_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'timestamp' => date('Y-m-d H:i:s')
            ];

            error_log("NovelManager: Manual content edit - " . json_encode($logData));

            $this->db->commit();

            // Return updated chapter data
            $updatedChapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE id = ?",
                [$chapter['id']]
            );

            return [
                'success' => true,
                'message' => 'Chapter content updated successfully',
                'chapter' => $updatedChapter,
                'previous_length' => $previousLength,
                'new_length' => $newLength,
                'content_stats' => $contentStats,
                'chunking_result' => $chunkingResult,
                'furigana_detected' => isset($updateData['original_content_with_furigana']),
                'furigana_count' => $updateData['furigana_count'] ?? 0
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create manual novel entry
     */
    public function createManualNovel(array $novelData): array {
        try {
            $this->db->beginTransaction();

            // Validate required fields for manual entry
            $requiredFields = ['original_title', 'original_synopsis'];
            foreach ($requiredFields as $field) {
                if (!isset($novelData[$field]) || empty(trim($novelData[$field]))) {
                    throw new Exception("Field '{$field}' is required for manual novel entry");
                }
            }

            // Prepare insert data for manual novel
            $insertData = [
                'url' => $novelData['source_url'] ?? 'manual://novel-' . time(),
                'platform' => 'manual',
                'original_title' => processTitleText($novelData['original_title']),
                'translated_title' => isset($novelData['translated_title']) ? processTitleText($novelData['translated_title']) : null,
                'author' => isset($novelData['author']) ? sanitizeInput($novelData['author'], true) : null,
                'publication_date' => isset($novelData['publication_date']) ? $novelData['publication_date'] : null,
                'total_chapters' => 0, // Will be updated as chapters are added
                'original_synopsis' => trim($novelData['original_synopsis']),
                'translated_synopsis' => isset($novelData['translated_synopsis']) ? trim($novelData['translated_synopsis']) : null,
                'status' => 'active'
            ];

            // Check if a manual novel with the same title already exists
            $existing = $this->db->fetchOne(
                "SELECT id FROM novels WHERE platform = 'manual' AND original_title = ?",
                [$insertData['original_title']]
            );

            if ($existing) {
                throw new Exception("A manual novel with this title already exists");
            }

            // Insert novel
            $novelId = $this->db->insert('novels', $insertData);

            $this->db->commit();

            return [
                'success' => true,
                'novel_id' => $novelId,
                'message' => 'Manual novel created successfully'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get chapters missing content for a novel or all novels
     */
    public function getChaptersMissingContent(int $novelId = null, array $options = []): array {
        try {
            $whereConditions = ["(c.original_content IS NULL OR c.original_content = '')"];
            $params = [];

            // Filter by novel if specified
            if ($novelId !== null) {
                $whereConditions[] = "c.novel_id = ?";
                $params[] = $novelId;
            }

            // Filter by platform if specified
            if (isset($options['platform']) && !empty($options['platform'])) {
                $whereConditions[] = "n.platform = ?";
                $params[] = $options['platform'];
            }

            // Filter by translation status if specified
            if (isset($options['translation_status']) && !empty($options['translation_status'])) {
                $whereConditions[] = "c.translation_status = ?";
                $params[] = $options['translation_status'];
            }

            // Build ORDER BY clause
            $orderBy = "n.id, c.chapter_number";
            if (isset($options['order_by'])) {
                switch ($options['order_by']) {
                    case 'novel_title':
                        $orderBy = "n.original_title, c.chapter_number";
                        break;
                    case 'chapter_number':
                        $orderBy = "c.chapter_number";
                        break;
                    case 'created_date':
                        $orderBy = "c.created_at DESC";
                        break;
                    default:
                        $orderBy = "n.id, c.chapter_number";
                }
            }

            // Build LIMIT clause
            $limit = "";
            if (isset($options['limit']) && is_numeric($options['limit'])) {
                $limit = "LIMIT " . (int)$options['limit'];
            }

            $whereClause = implode(" AND ", $whereConditions);

            $query = "
                SELECT
                    c.id as chapter_id,
                    c.novel_id,
                    c.chapter_number,
                    c.chapter_url,
                    c.original_title,
                    c.translated_title,
                    c.translation_status,
                    c.created_at as chapter_created_at,
                    n.original_title as novel_title,
                    n.translated_title as novel_translated_title,
                    n.platform,
                    n.status as novel_status,
                    n.total_chapters
                FROM chapters c
                INNER JOIN novels n ON c.novel_id = n.id
                WHERE {$whereClause}
                ORDER BY {$orderBy}
                {$limit}
            ";

            $chapters = $this->db->fetchAll($query, $params);

            // Add summary statistics
            $stats = [
                'total_missing' => count($chapters),
                'by_novel' => [],
                'by_platform' => []
            ];

            foreach ($chapters as $chapter) {
                // Count by novel
                $novelKey = $chapter['novel_id'];
                if (!isset($stats['by_novel'][$novelKey])) {
                    $stats['by_novel'][$novelKey] = [
                        'novel_title' => $chapter['novel_title'],
                        'count' => 0
                    ];
                }
                $stats['by_novel'][$novelKey]['count']++;

                // Count by platform
                $platform = $chapter['platform'];
                if (!isset($stats['by_platform'][$platform])) {
                    $stats['by_platform'][$platform] = 0;
                }
                $stats['by_platform'][$platform]++;
            }

            return [
                'success' => true,
                'chapters' => $chapters,
                'statistics' => $stats
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update existing chapter with content
     */
    public function updateChapterContent(int $novelId, int $chapterNumber, array $contentData): array {
        try {
            $this->db->beginTransaction();

            // Validate novel exists
            $novel = $this->db->fetchOne(
                "SELECT * FROM novels WHERE id = ?",
                [$novelId]
            );

            if (!$novel) {
                throw new Exception("Novel not found");
            }

            // Get existing chapter
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if (!$chapter) {
                throw new Exception("Chapter {$chapterNumber} not found for this novel");
            }

            // Validate required content
            if (!isset($contentData['original_content']) || empty(trim($contentData['original_content']))) {
                throw new Exception("Original content is required");
            }

            $originalContent = trim($contentData['original_content']);

            // Calculate content statistics
            $contentStats = $this->chapterChunker->calculateContentStats($originalContent);

            // Prepare update data
            $updateData = [
                'original_content' => $originalContent,
                'word_count' => $contentStats['word_count'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Update title if provided
            if (isset($contentData['original_title']) && !empty(trim($contentData['original_title']))) {
                $updateData['original_title'] = processTitleText($contentData['original_title']);
            }

            // Update translated title if provided
            if (isset($contentData['translated_title']) && !empty(trim($contentData['translated_title']))) {
                $updateData['translated_title'] = processTitleText($contentData['translated_title']);
            }

            // Handle translated content if provided
            if (isset($contentData['translated_content']) && !empty(trim($contentData['translated_content']))) {
                $updateData['translated_content'] = trim($contentData['translated_content']);
                $updateData['translation_status'] = 'completed';
                $updateData['translation_date'] = date('Y-m-d H:i:s');
            } else {
                // Clear existing translation if updating content
                $updateData['translated_content'] = null;
                $updateData['translation_status'] = 'pending';
                $updateData['translation_date'] = null;
            }

            // Check if content contains furigana markup and process it
            $furiganaService = FuriganaService::getInstance();
            if ($furiganaService && preg_match('/\{[^|]+\|[^}]+\}/', $originalContent)) {
                $updateData['original_content_with_furigana'] = $originalContent;
                $updateData['furigana_processing_status'] = 'detected';

                try {
                    // Process furigana data
                    $furiganaResult = $furiganaService->processChapterFurigana($chapter['id'], $originalContent);
                    if ($furiganaResult['success']) {
                        $updateData['furigana_count'] = $furiganaResult['furigana_count'];
                        $updateData['furigana_processing_status'] = 'processed';
                    }
                } catch (Exception $e) {
                    error_log("Warning: Chapter content update furigana processing failed: " . $e->getMessage());
                    $updateData['furigana_processing_status'] = 'error';
                }

                // Clean content for storage (remove furigana markup for base content)
                $updateData['original_content'] = $furiganaService->removeFurigana($originalContent);
            }

            // Clear existing chunks before updating content
            $this->db->query("DELETE FROM chapter_chunks WHERE chapter_id = ?", [$chapter['id']]);

            // Clear existing WordPress posts for this chapter
            $this->db->query("DELETE FROM wordpress_posts WHERE chapter_id = ?", [$chapter['id']]);

            // Update chapter
            $this->db->update('chapters', $updateData, 'id = ?', [$chapter['id']]);

            // Check if chapter needs chunking and create chunks if necessary
            $chunkingResult = null;
            if ($contentStats['needs_chunking']) {
                try {
                    // Use the cleaned content for chunking (same as what's stored in database)
                    $contentForChunking = $updateData['original_content'];
                    $chunkingResult = $this->chapterChunker->splitChapter($chapter['id'], $contentForChunking);
                    if ($chunkingResult['success']) {
                        error_log("NovelManager: Chapter content update chunking successful - created {$chunkingResult['chunks_created']} chunks");
                    }
                } catch (Exception $e) {
                    error_log("NovelManager: Chapter content update chunking failed: " . $e->getMessage());
                    // Don't fail the update operation if chunking fails
                    $chunkingResult = [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'chunks_created' => 0
                    ];
                }
            }

            $this->db->commit();

            // Get updated chapter data
            $updatedChapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE id = ?",
                [$chapter['id']]
            );

            return [
                'success' => true,
                'message' => 'Chapter content updated successfully',
                'chapter' => $updatedChapter,
                'furigana_detected' => isset($updateData['original_content_with_furigana']),
                'furigana_count' => $updateData['furigana_count'] ?? 0,
                'content_stats' => $contentStats,
                'chunking_result' => $chunkingResult
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Add manual chapter to a novel
     */
    public function addManualChapter(int $novelId, array $chapterData): array {
        try {
            $this->db->beginTransaction();

            // Validate novel exists (allow manual chapter creation for all platforms as fallback)
            $novel = $this->db->fetchOne(
                "SELECT * FROM novels WHERE id = ?",
                [$novelId]
            );

            if (!$novel) {
                throw new Exception("Novel not found");
            }

            // Validate required fields for manual chapter
            $requiredFields = ['chapter_number', 'original_title', 'original_content'];
            foreach ($requiredFields as $field) {
                if (!isset($chapterData[$field]) || empty(trim($chapterData[$field]))) {
                    throw new Exception("Field '{$field}' is required for manual chapter entry");
                }
            }

            $chapterNumber = (int)$chapterData['chapter_number'];

            // Check if chapter already exists
            $existing = $this->db->fetchOne(
                "SELECT id FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if ($existing) {
                throw new Exception("Chapter {$chapterNumber} already exists for this novel");
            }

            // Calculate content statistics
            $originalContent = trim($chapterData['original_content']);
            $contentStats = $this->chapterChunker->calculateContentStats($originalContent);

            // Prepare chapter insert data
            $insertData = [
                'novel_id' => $novelId,
                'chapter_number' => $chapterNumber,
                'chapter_url' => null, // Manual chapters don't have URLs
                'original_title' => processTitleText($chapterData['original_title']),
                'translated_title' => isset($chapterData['translated_title']) ? processTitleText($chapterData['translated_title']) : null,
                'original_content' => $originalContent,
                'translated_content' => isset($chapterData['translated_content']) ? trim($chapterData['translated_content']) : null,
                'word_count' => $contentStats['word_count'],
                'translation_status' => isset($chapterData['translated_content']) ? 'completed' : 'pending'
            ];

            // Check if content contains furigana markup and process it (same as regular saveChapter)
            $furiganaService = FuriganaService::getInstance();
            if ($furiganaService && preg_match('/\{[^|]+\|[^}]+\}/', $originalContent)) {
                $insertData['original_content_with_furigana'] = $originalContent;
                $insertData['furigana_processing_status'] = 'detected';

                try {
                    // Process furigana data after chapter is created
                    $furiganaProcessingPending = true;
                } catch (Exception $e) {
                    // If furigana processing fails, log error but continue
                    error_log("Warning: Manual chapter furigana processing failed: " . $e->getMessage());
                    $insertData['furigana_processing_status'] = 'error';
                }

                // Clean content for storage (remove furigana markup for base content)
                $insertData['original_content'] = $furiganaService->removeFurigana($originalContent);
            }

            // Insert chapter
            $chapterId = $this->db->insert('chapters', $insertData);

            // Process furigana if it was detected
            if (isset($furiganaProcessingPending) && $furiganaProcessingPending) {
                try {
                    $furiganaResult = $furiganaService->processChapterFurigana($chapterId, $originalContent);
                    if ($furiganaResult['success']) {
                        $this->db->update('chapters', [
                            'furigana_count' => $furiganaResult['furigana_count'],
                            'furigana_processing_status' => 'processed'
                        ], 'id = ?', [$chapterId]);
                    }
                } catch (Exception $e) {
                    error_log("Warning: Manual chapter furigana processing failed: " . $e->getMessage());
                    $this->db->update('chapters', [
                        'furigana_processing_status' => 'error'
                    ], 'id = ?', [$chapterId]);
                }
            }

            // Update novel's total chapter count
            $this->db->query(
                "UPDATE novels SET total_chapters = (SELECT COUNT(*) FROM chapters WHERE novel_id = ?) WHERE id = ?",
                [$novelId, $novelId]
            );

            // Check if chapter needs chunking and create chunks if necessary
            $chunkingResult = null;
            if ($contentStats['needs_chunking']) {
                try {
                    // Use the cleaned content for chunking (same as what's stored in database)
                    $contentForChunking = $insertData['original_content'];
                    $chunkingResult = $this->chapterChunker->splitChapter($chapterId, $contentForChunking);
                    if ($chunkingResult['success']) {
                        error_log("NovelManager: Manual chapter chunking successful - created {$chunkingResult['chunks_created']} chunks");
                    }
                } catch (Exception $e) {
                    error_log("NovelManager: Manual chapter chunking failed: " . $e->getMessage());
                    // Don't fail the save operation if chunking fails
                    $chunkingResult = [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'chunks_created' => 0
                    ];
                }
            }

            $this->db->commit();

            return [
                'success' => true,
                'chapter_id' => $chapterId,
                'chapter_number' => $chapterNumber,
                'message' => 'Manual chapter added successfully',
                'content_stats' => $contentStats,
                'chunking_result' => $chunkingResult
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update novel information
     */
    public function updateNovel(int $novelId, array $data): array {
        try {
            $allowedFields = [
                'translated_title', 'original_synopsis', 'translated_synopsis', 'status'
            ];

            $updateData = [];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    // Sanitize input
                    if ($field === 'translated_title') {
                        $updateData[$field] = trim($data[$field]);
                    } elseif ($field === 'original_synopsis') {
                        $updateData[$field] = trim($data[$field]);
                    } else {
                        $updateData[$field] = $data[$field];
                    }
                }
            }

            if (empty($updateData)) {
                throw new Exception("No valid fields to update");
            }

            // Validate translated_title if provided
            if (isset($updateData['translated_title']) && empty($updateData['translated_title'])) {
                throw new Exception("Translated title cannot be empty");
            }

            $updated = $this->db->update('novels', $updateData, 'id = ?', [$novelId]);

            if ($updated === 0) {
                throw new Exception("Novel not found or no changes made");
            }

            // Return updated novel data
            $updatedNovel = $this->db->fetchOne(
                "SELECT * FROM novels WHERE id = ?",
                [$novelId]
            );

            return [
                'success' => true,
                'message' => 'Novel updated successfully',
                'data' => $updatedNovel
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Translate novel synopsis
     */
    public function translateSynopsis(int $novelId, string $targetLanguage = 'en'): array {
        try {
            error_log("NovelManager: translateSynopsis called - Novel ID: $novelId, Target Language: $targetLanguage");

            $novel = $this->db->fetchOne(
                "SELECT * FROM novels WHERE id = ?",
                [$novelId]
            );

            if (!$novel) {
                error_log("NovelManager: Novel not found - ID: $novelId");
                throw new Exception("Novel not found");
            }

            error_log("NovelManager: Novel found - Title: " . $novel['original_title']);
            error_log("NovelManager: Original synopsis length: " . strlen($novel['original_synopsis'] ?? ''));

            if (empty($novel['original_synopsis'])) {
                error_log("NovelManager: No original synopsis to translate");
                throw new Exception("No original synopsis to translate");
            }

            error_log("NovelManager: Starting translation of synopsis");

            // Translate the synopsis
            $translationResult = $this->translationService->translateText(
                $novel['original_synopsis'],
                $targetLanguage,
                'auto',
                ['type' => 'synopsis']
            );

            error_log("NovelManager: Translation service result - Success: " . ($translationResult['success'] ? 'true' : 'false'));

            if (!$translationResult['success']) {
                error_log("NovelManager: Translation failed - Error: " . $translationResult['error']);
                throw new Exception("Translation failed: " . $translationResult['error']);
            }

            error_log("NovelManager: Translation successful, updating database");

            // Update the novel with the translated synopsis
            $updateData = [
                'translated_synopsis' => $translationResult['translated_text']
            ];

            $updated = $this->db->update('novels', $updateData, 'id = ?', [$novelId]);

            if ($updated === 0) {
                error_log("NovelManager: Failed to update database - no rows affected");
                throw new Exception("Failed to update translated synopsis");
            }

            error_log("NovelManager: Database updated successfully");

            // Return updated novel data
            $updatedNovel = $this->db->fetchOne(
                "SELECT * FROM novels WHERE id = ?",
                [$novelId]
            );

            error_log("NovelManager: Translation completed successfully");

            return [
                'success' => true,
                'message' => 'Synopsis translated successfully',
                'data' => $updatedNovel,
                'translation_result' => $translationResult
            ];

        } catch (Exception $e) {
            error_log("NovelManager: translateSynopsis exception - " . $e->getMessage());
            error_log("NovelManager: Exception trace - " . $e->getTraceAsString());

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update name dictionary entry
     */
    public function updateNameDictionary(int $novelId, int $nameId, array $data): array {
        try {
            $allowedFields = ['romanization', 'translation', 'name_type', 'is_verified'];
            
            $updateData = [];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            if (empty($updateData)) {
                throw new Exception("No valid fields to update");
            }
            
            $updated = $this->db->update(
                'name_dictionary', 
                $updateData, 
                'id = ? AND novel_id = ?', 
                [$nameId, $novelId]
            );
            
            if ($updated === 0) {
                throw new Exception("Name entry not found or no changes made");
            }
            
            return [
                'success' => true,
                'message' => 'Name dictionary updated successfully'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Add new name to dictionary
     */
    public function addNameToDictionary(int $novelId, array $data): array {
        try {
            // Validate required fields
            if (empty($data['original_name'])) {
                throw new Exception("Original name is required");
            }

            // Verify novel exists
            $novel = $this->db->fetchOne("SELECT id FROM novels WHERE id = ?", [$novelId]);
            if (!$novel) {
                throw new Exception("Novel not found");
            }

            // Check for duplicate names
            $existingName = $this->db->fetchOne(
                "SELECT id FROM name_dictionary WHERE novel_id = ? AND original_name = ?",
                [$novelId, $data['original_name']]
            );

            if ($existingName) {
                throw new Exception("A name with this original text already exists in the dictionary");
            }

            // Prepare insert data
            $insertData = [
                'novel_id' => $novelId,
                'original_name' => $data['original_name'],
                'name_type' => $data['name_type'] ?? 'other',
                'frequency' => 1, // Start with frequency of 1 for manually added names
                'first_appearance_chapter' => null, // No specific chapter for manually added names
                'is_verified' => true // Manually added names are considered verified
            ];

            // Add optional fields if provided
            if (!empty($data['romanization'])) {
                $insertData['romanization'] = $data['romanization'];
            }

            if (!empty($data['translation'])) {
                $insertData['translation'] = $data['translation'];
            }

            $nameId = $this->db->insert('name_dictionary', $insertData);

            if (!$nameId) {
                throw new Exception("Failed to add name to dictionary");
            }

            return [
                'success' => true,
                'message' => 'Name added successfully',
                'name_id' => $nameId
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete name from dictionary
     */
    public function deleteName(int $novelId, int $nameId): array {
        try {
            // First check if the name exists and belongs to the novel
            $name = $this->db->fetchOne(
                "SELECT original_name FROM name_dictionary WHERE id = ? AND novel_id = ?",
                [$nameId, $novelId]
            );

            if (!$name) {
                throw new Exception("Name not found or does not belong to this novel");
            }

            $deleted = $this->db->delete(
                'name_dictionary',
                'id = ? AND novel_id = ?',
                [$nameId, $novelId]
            );

            if ($deleted === 0) {
                throw new Exception("Failed to delete name");
            }

            return [
                'success' => true,
                'message' => 'Name deleted successfully'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete novel and all related data
     */
    public function deleteNovel(int $novelId): array {
        try {
            $this->db->beginTransaction();

            // Check if novel exists
            $novel = $this->db->fetchOne("SELECT id, original_title FROM novels WHERE id = ?", [$novelId]);
            if (!$novel) {
                throw new Exception("Novel not found");
            }

            // Delete related data in correct order
            $this->db->query("DELETE FROM name_dictionary WHERE novel_id = ?", [$novelId]);
            $this->db->query("DELETE FROM translation_logs WHERE novel_id = ?", [$novelId]);
            $this->db->query("DELETE FROM chapters WHERE novel_id = ?", [$novelId]);
            $this->db->query("DELETE FROM novels WHERE id = ?", [$novelId]);

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'Novel deleted successfully'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get appropriate crawler for platform
     */
    public function getCrawler(string $platform): BaseCrawler {
        switch ($platform) {
            case 'syosetu':
                return new SyosetuCrawler();
            case 'kakuyomu':
                return new KakuyomuCrawler();
            case 'shuba69':
                return new Shuba69Crawler();
            default:
                throw new Exception("Unsupported platform: {$platform}");
        }
    }

    /**
     * Translate multiple chapters (bulk operation)
     */
    public function translateChapters(int $novelId, array $chapterNumbers, string $targetLanguage = 'en', array $context = []): array {
        $results = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($chapterNumbers as $chapterNumber) {
            try {
                $result = $this->translateChapter($novelId, $chapterNumber, $targetLanguage, $context);
                $results[] = [
                    'chapter_number' => $chapterNumber,
                    'success' => $result['success'],
                    'error' => $result['error'] ?? null
                ];

                if ($result['success']) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            } catch (Exception $e) {
                $results[] = [
                    'chapter_number' => $chapterNumber,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                $errorCount++;
            }
        }

        return [
            'success' => $errorCount === 0,
            'total_chapters' => count($chapterNumbers),
            'successful_translations' => $successCount,
            'failed_translations' => $errorCount,
            'results' => $results,
            'message' => $errorCount === 0
                ? "All {$successCount} chapters translated successfully"
                : "{$successCount} chapters translated, {$errorCount} failed"
        ];
    }

    /**
     * Re-translate chapters (clears existing translation and re-translates)
     */
    public function retranslateChapters(int $novelId, array $chapterNumbers, string $targetLanguage = 'en', array $context = []): array {
        try {
            $this->db->beginTransaction();

            // First clear existing translations
            $clearResult = $this->clearChapterTranslations($novelId, $chapterNumbers);
            if (!$clearResult['success']) {
                $this->db->rollback();
                return $clearResult;
            }

            // Then translate the chapters
            $translateResult = $this->translateChapters($novelId, $chapterNumbers, $targetLanguage, $context);

            if ($translateResult['success']) {
                $this->db->commit();
                return [
                    'success' => true,
                    'total_chapters' => $translateResult['total_chapters'],
                    'successful_translations' => $translateResult['successful_translations'],
                    'failed_translations' => $translateResult['failed_translations'],
                    'results' => $translateResult['results'],
                    'message' => "Re-translation completed: {$translateResult['message']}"
                ];
            } else {
                $this->db->rollback();
                return $translateResult;
            }

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => 'Re-translation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Translate chapter titles only (without content)
     */
    public function translateChapterTitles($novelId, $chapterNumbers, string $targetLanguage = 'en', bool $forceRetranslate = false): array {
        try {
            file_put_contents('debug.log', "NovelManager: translateChapterTitles - Starting for novel {$novelId}, force: " . ($forceRetranslate ? 'yes' : 'no') . "\n", FILE_APPEND);

            // Handle 'all' chapters case
            if ($chapterNumbers === 'all') {
                $whereClause = "novel_id = ? AND original_title IS NOT NULL AND original_title != ''";
                $params = [$novelId];

                // If not forcing retranslation, only get chapters without translated titles
                if (!$forceRetranslate) {
                    $whereClause .= " AND (translated_title IS NULL OR translated_title = '')";
                }

                $chapters = $this->db->fetchAll(
                    "SELECT id, chapter_number, original_title, translated_title
                     FROM chapters
                     WHERE {$whereClause}
                     ORDER BY chapter_number",
                    $params
                );
                $chapterNumbers = array_column($chapters, 'chapter_number');
            } else {
                // Ensure it's an array for single chapter case
                if (!is_array($chapterNumbers)) {
                    $chapterNumbers = [$chapterNumbers];
                }
            }

            if (empty($chapterNumbers)) {
                return [
                    'success' => false,
                    'error' => 'No chapters found to translate titles for'
                ];
            }

            file_put_contents('debug.log', "NovelManager: translateChapterTitles - Processing " . count($chapterNumbers) . " chapters\n", FILE_APPEND);

            $results = [];
            $successCount = 0;
            $errorCount = 0;
            $skippedCount = 0;

            // Get name dictionary for consistency
            $nameDictionary = $this->db->fetchAll(
                "SELECT * FROM name_dictionary WHERE novel_id = ? ORDER BY frequency DESC",
                [$novelId]
            );

            foreach ($chapterNumbers as $chapterNumber) {
                try {
                    $chapter = $this->db->fetchOne(
                        "SELECT id, chapter_number, original_title, translated_title
                         FROM chapters
                         WHERE novel_id = ? AND chapter_number = ?",
                        [$novelId, $chapterNumber]
                    );

                    if (!$chapter) {
                        $results[] = [
                            'chapter_number' => $chapterNumber,
                            'success' => false,
                            'error' => 'Chapter not found'
                        ];
                        $errorCount++;
                        continue;
                    }

                    if (empty($chapter['original_title'])) {
                        $results[] = [
                            'chapter_number' => $chapterNumber,
                            'success' => false,
                            'error' => 'No original title to translate'
                        ];
                        $errorCount++;
                        continue;
                    }

                    // Skip if already translated and not forcing retranslation
                    if (!$forceRetranslate && !empty($chapter['translated_title']) && trim($chapter['translated_title']) !== '') {
                        $results[] = [
                            'chapter_number' => $chapterNumber,
                            'success' => true,
                            'skipped' => true,
                            'original_title' => $chapter['original_title'],
                            'translated_title' => $chapter['translated_title'],
                            'message' => 'Already translated'
                        ];
                        $skippedCount++;
                        continue;
                    }

                    file_put_contents('debug.log', "NovelManager: translateChapterTitles - Translating chapter {$chapterNumber}: {$chapter['original_title']}\n", FILE_APPEND);

                    // Translate the title
                    $titleResult = $this->translationService->translateText(
                        $chapter['original_title'],
                        $targetLanguage,
                        'auto',
                        ['type' => 'title', 'names' => $nameDictionary]
                    );

                    if ($titleResult['success']) {
                        $translatedTitle = trim($titleResult['translated_text']);

                        if (!empty($translatedTitle)) {
                            // Update the chapter with translated title
                            $updated = $this->db->update(
                                'chapters',
                                ['translated_title' => $translatedTitle],
                                'id = ?',
                                [$chapter['id']]
                            );

                            if ($updated > 0) {
                                $results[] = [
                                    'chapter_number' => $chapterNumber,
                                    'success' => true,
                                    'original_title' => $chapter['original_title'],
                                    'translated_title' => $translatedTitle,
                                    'was_retranslated' => $forceRetranslate && !empty($chapter['translated_title'])
                                ];
                                $successCount++;
                                file_put_contents('debug.log', "NovelManager: translateChapterTitles - Successfully translated chapter {$chapterNumber}\n", FILE_APPEND);
                            } else {
                                $results[] = [
                                    'chapter_number' => $chapterNumber,
                                    'success' => false,
                                    'error' => 'Failed to update database'
                                ];
                                $errorCount++;
                            }
                        } else {
                            $results[] = [
                                'chapter_number' => $chapterNumber,
                                'success' => false,
                                'error' => 'Translation result was empty'
                            ];
                            $errorCount++;
                        }
                    } else {
                        $error = $titleResult['error'] ?? 'Unknown error';
                        file_put_contents('debug.log', "NovelManager: translateChapterTitles - Failed to translate chapter {$chapterNumber}: {$error}\n", FILE_APPEND);

                        // If translation failed due to truncation or API issues, use original title as fallback
                        if (strpos($error, 'truncation') !== false || strpos($error, 'token limits') !== false || strpos($error, 'Unknown error') !== false) {
                            file_put_contents('debug.log', "NovelManager: translateChapterTitles - Using original title as fallback for chapter {$chapterNumber}\n", FILE_APPEND);

                            $updated = $this->db->update(
                                'chapters',
                                ['translated_title' => $chapter['original_title']],
                                'id = ?',
                                [$chapter['id']]
                            );

                            if ($updated > 0) {
                                $results[] = [
                                    'chapter_number' => $chapterNumber,
                                    'success' => true,
                                    'original_title' => $chapter['original_title'],
                                    'translated_title' => $chapter['original_title'],
                                    'was_retranslated' => $forceRetranslate && !empty($chapter['translated_title']),
                                    'fallback_used' => true
                                ];
                                $successCount++;
                                file_put_contents('debug.log', "NovelManager: translateChapterTitles - Used original title as fallback for chapter {$chapterNumber}\n", FILE_APPEND);
                            } else {
                                $results[] = [
                                    'chapter_number' => $chapterNumber,
                                    'success' => false,
                                    'error' => 'Failed to update database with fallback title'
                                ];
                                $errorCount++;
                            }
                        } else {
                            $results[] = [
                                'chapter_number' => $chapterNumber,
                                'success' => false,
                                'error' => 'Translation failed: ' . $error
                            ];
                            $errorCount++;
                        }
                    }

                } catch (Exception $e) {
                    $results[] = [
                        'chapter_number' => $chapterNumber,
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    $errorCount++;
                    file_put_contents('debug.log', "NovelManager: translateChapterTitles - Exception for chapter {$chapterNumber}: " . $e->getMessage() . "\n", FILE_APPEND);
                }
            }

            $message = [];
            if ($successCount > 0) {
                $message[] = "{$successCount} titles translated successfully";
            }
            if ($skippedCount > 0) {
                $message[] = "{$skippedCount} titles already translated";
            }
            if ($errorCount > 0) {
                $message[] = "{$errorCount} failed";
            }

            return [
                'success' => $errorCount === 0,
                'total_chapters' => count($chapterNumbers),
                'successful_translations' => $successCount,
                'failed_translations' => $errorCount,
                'skipped_translations' => $skippedCount,
                'results' => $results,
                'message' => implode(', ', $message)
            ];

        } catch (Exception $e) {
            file_put_contents('debug.log', "NovelManager: translateChapterTitles - Exception: " . $e->getMessage() . "\n", FILE_APPEND);
            return [
                'success' => false,
                'error' => 'Title translation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Translate a single chunk
     */
    public function translateSingleChunk(int $chunkId, string $targetLanguage = 'en', array $context = []): array {
        try {
            $db = Database::getInstance();

            // Get chunk data with chapter and novel info
            $chunk = $db->fetchOne(
                "SELECT cc.*, c.novel_id, c.chapter_number
                 FROM chapter_chunks cc
                 JOIN chapters c ON cc.chapter_id = c.id
                 WHERE cc.id = ?",
                [$chunkId]
            );

            if (!$chunk) {
                throw new Exception("Chunk not found");
            }

            if (empty($chunk['original_content'])) {
                throw new Exception("Chunk has no content to translate");
            }

            // Check if chunk is already being translated
            if ($chunk['translation_status'] === 'translating') {
                throw new Exception("Chunk is already being translated");
            }

            // Update chunk status to translating
            $db->update('chapter_chunks',
                ['translation_status' => 'translating'],
                'id = ?',
                [$chunkId]
            );

            // Get name dictionary for consistency
            $nameDictionary = $this->translationService->getNameDictionary($chunk['novel_id']);

            // Get context from adjacent chunks
            $context = $this->getChunkTranslationContext($chunkId, $chunk['chapter_id'], $chunk['chunk_number']);

            // Analyze narrative context for this chunk
            $narrativeContext = $this->analyzeChunkNarrativeContext(
                $chunk['original_content'],
                $chunk['chunk_number'],
                $chunk['chapter_id']
            );

            // Prepare translation context
            $translationContext = [
                'type' => 'chunk',
                'names' => $nameDictionary,
                'chunk_number' => $chunk['chunk_number'],
                'chapter_number' => $chunk['chapter_number'],
                'narrative_context' => $narrativeContext
            ];

            // Add POV preference if provided
            if (isset($context['user_pov_preference'])) {
                $translationContext['user_pov_preference'] = $context['user_pov_preference'];
            }

            // Add adjacent chunk context if available
            if (!empty($context['previous_content'])) {
                $translationContext['previous_context'] = $context['previous_content'];
            }
            if (!empty($context['next_content'])) {
                $translationContext['next_context'] = $context['next_context'];
            }
            if (!empty($context['previous_translation'])) {
                $translationContext['previous_translation'] = $context['previous_translation'];
            }
            if (!empty($context['previous_narrative_context'])) {
                $translationContext['previous_narrative_context'] = $context['previous_narrative_context'];
            }

            // Translate the chunk
            $translationResult = $this->translationService->translateText(
                $chunk['original_content'],
                $targetLanguage,
                'auto',
                $translationContext
            );

            if (!$translationResult['success']) {
                // Mark chunk as error
                $db->update('chapter_chunks',
                    ['translation_status' => 'error'],
                    'id = ?',
                    [$chunkId]
                );

                throw new Exception("Translation failed: " . $translationResult['error']);
            }

            // ENHANCED: Extract names from this individual chunk for incremental name discovery
            $chunkNewNames = [];
            try {
                // Extract new names from this chunk's original and translated content
                $chunkNewNames = $this->translationService->extractNamesFromChunk(
                    $chunk['original_content'],
                    $translationResult['translated_text']
                );

                if (!empty($chunkNewNames)) {
                    // Update name dictionary with names from this chunk
                    $this->translationService->updateNameDictionaryFromChunk(
                        $chunk['novel_id'],
                        $chunkNewNames,
                        $chunk['chapter_number']
                    );
                }
            } catch (Exception $e) {
                // Log error but don't fail the translation
                error_log("Warning: Failed to extract names from chunk {$chunkId}: " . $e->getMessage());
            }

            // Update chunk with translation
            $db->update('chapter_chunks', [
                'translated_content' => $translationResult['translated_text'],
                'translation_status' => 'completed',
                'translation_date' => date('Y-m-d H:i:s')
            ], 'id = ?', [$chunkId]);

            // Check if all chunks in chapter are completed and update chapter status
            $this->updateChapterStatusFromChunks($chunk['chapter_id']);

            return [
                'success' => true,
                'message' => 'Chunk translated successfully',
                'chunk_id' => $chunkId,
                'chunk_number' => $chunk['chunk_number'],
                'translated_text' => $translationResult['translated_text'],
                'character_count' => $translationResult['character_count'] ?? mb_strlen($translationResult['translated_text']),
                'execution_time' => $translationResult['execution_time'] ?? 0,
                'new_names_found' => count($chunkNewNames),
                'names_extracted' => !empty($chunkNewNames)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get translation context for a chunk from adjacent chunks
     */
    private function getChunkTranslationContext(int $chunkId, int $chapterId, int $chunkNumber): array {
        $context = [];

        try {
            $db = Database::getInstance();

            // Get previous chunk content (for context)
            if ($chunkNumber > 1) {
                $prevChunk = $db->fetchOne(
                    "SELECT original_content, translated_content FROM chapter_chunks
                     WHERE chapter_id = ? AND chunk_number = ?",
                    [$chapterId, $chunkNumber - 1]
                );

                if ($prevChunk) {
                    $context['previous_content'] = mb_substr($prevChunk['original_content'], -300);
                    if ($prevChunk['translated_content']) {
                        $context['previous_translation'] = mb_substr($prevChunk['translated_content'], -200);
                    }

                    // Analyze narrative context of previous chunk for consistency
                    $context['previous_narrative_context'] = $this->analyzeChunkNarrativeContext(
                        $prevChunk['original_content'],
                        $chunkNumber - 1,
                        $chapterId
                    );
                }
            }

            // Get next chunk content (for context)
            $nextChunk = $db->fetchOne(
                "SELECT original_content FROM chapter_chunks
                 WHERE chapter_id = ? AND chunk_number = ?",
                [$chapterId, $chunkNumber + 1]
            );

            if ($nextChunk) {
                $context['next_content'] = mb_substr($nextChunk['original_content'], 0, 300);
            }

        } catch (Exception $e) {
            // If context retrieval fails, continue without context
            error_log("Warning: Failed to get chunk context: " . $e->getMessage());
        }

        return $context;
    }

    /**
     * Analyze narrative context for a chunk
     */
    private function analyzeChunkNarrativeContext(string $content, int $chunkNumber, int $chapterId): array {
        $context = [
            'has_scene_transition' => false,
            'has_time_transition' => false,
            'narrative_voice' => 'third_person',
            'tense' => 'past',
            'narrative_confidence' => 0.0,
            'perspective_indicators' => []
        ];

        // Check for scene transitions
        if (preg_match('/[＊\*]{3,}|[-=]{5,}/', $content)) {
            $context['has_scene_transition'] = true;
        }

        // Check for time transitions
        $timePatterns = [
            '/(?:翌日|次の日|その後|しばらく|数時間|数分|朝|昼|夜|夕方)/u',
            '/(?:meanwhile|later|next day|hours later|morning|evening)/i'
        ];

        foreach ($timePatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $context['has_time_transition'] = true;
                break;
            }
        }

        // Enhanced narrative voice detection with multi-language support
        $narrativeAnalysis = $this->detectNarrativePerspective($content);
        $context['narrative_voice'] = $narrativeAnalysis['perspective'];
        $context['narrative_confidence'] = $narrativeAnalysis['confidence'];
        $context['perspective_indicators'] = $narrativeAnalysis['indicators'];

        // Check consistency with previous chunks in the same chapter
        if ($chunkNumber > 1) {
            try {
                $db = Database::getInstance();
                $prevChunk = $db->fetchOne(
                    "SELECT original_content FROM chapter_chunks
                     WHERE chapter_id = ? AND chunk_number = ?",
                    [$chapterId, $chunkNumber - 1]
                );

                if ($prevChunk) {
                    $prevNarrativeAnalysis = $this->detectNarrativePerspective($prevChunk['original_content']);

                    // If there's a perspective mismatch, use the more confident detection
                    if ($prevNarrativeAnalysis['perspective'] !== $context['narrative_voice']) {
                        if ($prevNarrativeAnalysis['confidence'] > $context['narrative_confidence']) {
                            $context['narrative_voice'] = $prevNarrativeAnalysis['perspective'];
                            $context['perspective_override'] = true;
                            $context['override_reason'] = 'consistency_with_previous_chunk';
                        }
                    }
                }
            } catch (Exception $e) {
                // Continue without consistency check if it fails
                error_log("Warning: Failed to check narrative consistency: " . $e->getMessage());
            }
        }

        return $context;
    }

    /**
     * Detect narrative perspective with enhanced multi-language support
     */
    private function detectNarrativePerspective(string $content): array {
        $firstPersonIndicators = [];
        $thirdPersonIndicators = [];
        $confidence = 0.0;

        // Japanese first-person pronouns and patterns
        $japaneseFirstPerson = [
            '/(?:私|僕|俺|わたし|ぼく|おれ)(?:は|が|を|に|の|と|で|から|まで)/u' => 1.0,
            '/(?:私|僕|俺)(?:たち|達)(?:は|が|を|に)/u' => 0.9,
            '/(?:私|僕|俺)(?:自身|じしん)/u' => 0.8,
            '/(?:私|僕|俺)(?:の|が)(?:心|気持ち|思い)/u' => 0.7
        ];

        // Chinese first-person pronouns
        $chineseFirstPerson = [
            '/(?:我|我们|咱|咱们)(?:走|去|来|到|在|有|要|会|能|可以|是|的)/u' => 1.0,
            '/(?:我|我们)(?:觉得|认为|想|希望|需要|担心|问道|说)/u' => 0.9,
            '/(?:我|我们)(?:自己|本人)/u' => 0.8,
            '/(?:我|我们)(?:很|非常|特别|十分)/u' => 0.7
        ];

        // Korean first-person pronouns
        $koreanFirstPerson = [
            '/(?:나|내|저|제|우리)(?:는|가|를|을|의|에|와|과)/u' => 1.0,
            '/(?:나|저)(?:자신|스스로)/u' => 0.8,
            '/(?:우리|저희)(?:는|가|를)/u' => 0.9
        ];

        // Japanese third-person indicators
        $japaneseThirdPerson = [
            '/(?:彼|彼女|彼ら|彼女ら)(?:は|が|を|に)/u' => 0.8,
            '/(?:[ぁ-んァ-ヶ一-龯]{2,6})(?:は|が)(?:思った|考えた|感じた)/u' => 0.6,
            '/(?:[ぁ-んァ-ヶ一-龯]{2,6})(?:の|が)(?:心|気持ち|思い)/u' => 0.5
        ];

        // Check for first-person patterns
        foreach (array_merge($japaneseFirstPerson, $chineseFirstPerson, $koreanFirstPerson) as $pattern => $weight) {
            if (preg_match_all($pattern, $content, $matches)) {
                $count = count($matches[0]);
                $firstPersonIndicators = array_merge($firstPersonIndicators, $matches[0]);
                $confidence += $count * $weight;
            }
        }

        // Check for third-person patterns
        foreach ($japaneseThirdPerson as $pattern => $weight) {
            if (preg_match_all($pattern, $content, $matches)) {
                $count = count($matches[0]);
                $thirdPersonIndicators = array_merge($thirdPersonIndicators, $matches[0]);
                $confidence -= $count * $weight; // Negative for third person
            }
        }

        // Determine perspective based on confidence score
        if ($confidence > 0.5) {
            $perspective = 'first_person';
            $finalConfidence = min($confidence / 3.0, 1.0); // Normalize confidence
        } elseif ($confidence < -0.5) {
            $perspective = 'third_person';
            $finalConfidence = min(abs($confidence) / 3.0, 1.0);
        } else {
            // Default to third person with low confidence
            $perspective = 'third_person';
            $finalConfidence = 0.3;
        }

        return [
            'perspective' => $perspective,
            'confidence' => $finalConfidence,
            'indicators' => [
                'first_person' => array_unique($firstPersonIndicators),
                'third_person' => array_unique($thirdPersonIndicators)
            ]
        ];
    }

    /**
     * Update chapter translation status based on chunk completion
     */
    private function updateChapterStatusFromChunks(int $chapterId): void {
        try {
            $db = Database::getInstance();

            // Get chunk status summary
            $statusSummary = $db->fetchOne(
                "SELECT
                    COUNT(*) as total_chunks,
                    COUNT(CASE WHEN translation_status = 'completed' THEN 1 END) as completed_chunks,
                    COUNT(CASE WHEN translation_status = 'error' THEN 1 END) as error_chunks,
                    COUNT(CASE WHEN translation_status = 'translating' THEN 1 END) as translating_chunks
                 FROM chapter_chunks
                 WHERE chapter_id = ?",
                [$chapterId]
            );

            if (!$statusSummary || $statusSummary['total_chunks'] == 0) {
                return; // No chunks, don't update chapter status
            }

            $newStatus = 'pending';
            $translatedContent = null;

            if ($statusSummary['completed_chunks'] == $statusSummary['total_chunks']) {
                // All chunks completed - reassemble content and translate title if needed
                $newStatus = 'completed';
                $translatedContent = $this->reassembleChapterFromChunks($chapterId);

                // Check if chapter title needs translation
                $this->ensureChapterTitleTranslated($chapterId);
            } elseif ($statusSummary['translating_chunks'] > 0) {
                $newStatus = 'translating';
            } elseif ($statusSummary['error_chunks'] > 0) {
                $newStatus = 'error';
            }

            // Update chapter status
            $updateData = ['translation_status' => $newStatus];
            if ($translatedContent !== null) {
                $updateData['translated_content'] = $translatedContent;
                $updateData['translation_date'] = date('Y-m-d H:i:s');
            }

            $db->update('chapters', $updateData, 'id = ?', [$chapterId]);

        } catch (Exception $e) {
            error_log("Warning: Failed to update chapter status from chunks: " . $e->getMessage());
        }
    }

    /**
     * Reassemble chapter content from completed chunks
     */
    private function reassembleChapterFromChunks(int $chapterId): string {
        try {
            $db = Database::getInstance();

            // Get all completed chunks in order
            $chunks = $db->fetchAll(
                "SELECT translated_content FROM chapter_chunks
                 WHERE chapter_id = ? AND translation_status = 'completed'
                 ORDER BY chunk_number",
                [$chapterId]
            );

            $translatedChunks = array_map(function($chunk) {
                return $chunk['translated_content'];
            }, $chunks);

            // Use the translation service's reassembly method if available
            if (method_exists($this->translationService, 'basicReassembly')) {
                return $this->translationService->basicReassembly($translatedChunks);
            }

            // Fallback to simple joining
            return implode("\n\n", array_filter($translatedChunks));

        } catch (Exception $e) {
            error_log("Warning: Failed to reassemble chapter from chunks: " . $e->getMessage());
            return '';
        }
    }

    /**
     * Ensure chapter title is translated when chunks are completed
     */
    private function ensureChapterTitleTranslated(int $chapterId): void {
        try {
            $db = Database::getInstance();

            // Get chapter info
            $chapter = $db->fetchOne(
                "SELECT id, novel_id, chapter_number, original_title, translated_title FROM chapters WHERE id = ?",
                [$chapterId]
            );

            if (!$chapter || empty($chapter['original_title'])) {
                file_put_contents('debug.log', "NovelManager: ensureChapterTitleTranslated - No chapter or no original title for chapter ID {$chapterId}\n", FILE_APPEND);
                return; // No title to translate
            }

            // Check if already translated and not empty
            if (!empty($chapter['translated_title']) && trim($chapter['translated_title']) !== '') {
                file_put_contents('debug.log', "NovelManager: ensureChapterTitleTranslated - Chapter {$chapter['chapter_number']} already has translated title\n", FILE_APPEND);
                return; // Already translated
            }

            file_put_contents('debug.log', "NovelManager: ensureChapterTitleTranslated - Starting title translation for chapter {$chapter['chapter_number']}: {$chapter['original_title']}\n", FILE_APPEND);

            // Get name dictionary for consistency
            $nameDictionary = $this->translationService->getNameDictionary($chapter['novel_id']);

            // Translate the title
            $titleResult = $this->translationService->translateText(
                $chapter['original_title'],
                'en',
                'auto',
                ['type' => 'title', 'names' => $nameDictionary]
            );

            if ($titleResult['success']) {
                $translatedTitle = trim($titleResult['translated_text']);

                if (!empty($translatedTitle)) {
                    $updated = $db->update(
                        'chapters',
                        ['translated_title' => $translatedTitle],
                        'id = ?',
                        [$chapterId]
                    );

                    if ($updated > 0) {
                        file_put_contents('debug.log', "NovelManager: Auto-translated chapter {$chapter['chapter_number']} title: {$translatedTitle}\n", FILE_APPEND);
                    } else {
                        file_put_contents('debug.log', "NovelManager: Failed to update database for chapter {$chapter['chapter_number']} title\n", FILE_APPEND);
                    }
                } else {
                    file_put_contents('debug.log', "NovelManager: Empty translation result for chapter {$chapter['chapter_number']} title\n", FILE_APPEND);
                }
            } else {
                $error = $titleResult['error'] ?? 'Unknown error';
                file_put_contents('debug.log', "NovelManager: Failed to auto-translate chapter {$chapter['chapter_number']} title: {$error}\n", FILE_APPEND);

                // If translation failed due to truncation or API issues, use original title as fallback
                if (strpos($error, 'truncation') !== false || strpos($error, 'token limits') !== false) {
                    file_put_contents('debug.log', "NovelManager: Using original title as fallback due to truncation issues\n", FILE_APPEND);

                    $updated = $db->update(
                        'chapters',
                        ['translated_title' => $chapter['original_title']],
                        'id = ?',
                        [$chapterId]
                    );

                    if ($updated) {
                        file_put_contents('debug.log', "NovelManager: Set original title as translated title for chapter {$chapter['chapter_number']}\n", FILE_APPEND);
                    }
                }
            }

        } catch (Exception $e) {
            file_put_contents('debug.log', "NovelManager: Exception in ensureChapterTitleTranslated: " . $e->getMessage() . "\n", FILE_APPEND);
        }
    }

    /**
     * Clear translated titles only (preserves original titles and content)
     */
    public function clearTranslatedTitles(int $novelId, $chapterNumbers = 'all'): array {
        try {
            $clearedCount = 0;
            $notFoundCount = 0;
            $results = [];

            // Build chapter selection query
            if ($chapterNumbers === 'all') {
                // Clear all translated titles for the novel
                $chapters = $this->db->fetchAll(
                    "SELECT id, chapter_number, original_title, translated_title FROM chapters WHERE novel_id = ? AND translated_title IS NOT NULL",
                    [$novelId]
                );
                file_put_contents('debug.log', "NovelManager: clearTranslatedTitles - Clearing all translated titles for novel {$novelId}, found " . count($chapters) . " chapters with translated titles\n", FILE_APPEND);
            } else {
                // Clear specific chapters
                if (!is_array($chapterNumbers)) {
                    $chapterNumbers = [$chapterNumbers];
                }

                $placeholders = str_repeat('?,', count($chapterNumbers) - 1) . '?';
                $chapters = $this->db->fetchAll(
                    "SELECT id, chapter_number, original_title, translated_title FROM chapters WHERE novel_id = ? AND chapter_number IN ({$placeholders})",
                    array_merge([$novelId], $chapterNumbers)
                );
                file_put_contents('debug.log', "NovelManager: clearTranslatedTitles - Clearing specific chapters: " . implode(',', $chapterNumbers) . " for novel {$novelId}\n", FILE_APPEND);
            }

            foreach ($chapters as $chapter) {
                $chapterNumber = $chapter['chapter_number'];

                // Only clear if there's actually a translated title
                if (empty($chapter['translated_title'])) {
                    $results[] = [
                        'chapter_number' => $chapterNumber,
                        'success' => true,
                        'message' => 'No translated title to clear',
                        'was_cleared' => false
                    ];
                    continue;
                }

                // Clear only the translated title
                $updated = $this->db->update(
                    'chapters',
                    ['translated_title' => null],
                    'id = ?',
                    [$chapter['id']]
                );

                if ($updated > 0) {
                    $results[] = [
                        'chapter_number' => $chapterNumber,
                        'success' => true,
                        'original_title' => $chapter['original_title'],
                        'previous_translated_title' => $chapter['translated_title'],
                        'was_cleared' => true
                    ];
                    $clearedCount++;
                    file_put_contents('debug.log', "NovelManager: clearTranslatedTitles - Cleared translated title for chapter {$chapterNumber}\n", FILE_APPEND);
                } else {
                    $results[] = [
                        'chapter_number' => $chapterNumber,
                        'success' => false,
                        'error' => 'Failed to update database'
                    ];
                    file_put_contents('debug.log', "NovelManager: clearTranslatedTitles - Failed to clear translated title for chapter {$chapterNumber}\n", FILE_APPEND);
                }
            }

            // Handle chapters that weren't found (only for specific chapter mode)
            if ($chapterNumbers !== 'all') {
                $foundChapterNumbers = array_column($chapters, 'chapter_number');
                $missingChapterNumbers = array_diff($chapterNumbers, $foundChapterNumbers);

                foreach ($missingChapterNumbers as $missingChapter) {
                    $results[] = [
                        'chapter_number' => $missingChapter,
                        'success' => false,
                        'error' => 'Chapter not found'
                    ];
                    $notFoundCount++;
                }
            }

            $totalRequested = $chapterNumbers === 'all' ? count($chapters) : count($chapterNumbers);
            $message = $chapterNumbers === 'all'
                ? "Cleared {$clearedCount} translated titles from all chapters"
                : "Cleared {$clearedCount} of {$totalRequested} requested translated titles";

            if ($notFoundCount > 0) {
                $message .= ", {$notFoundCount} chapters not found";
            }

            return [
                'success' => $notFoundCount === 0 || $clearedCount > 0,
                'total_chapters' => $totalRequested,
                'cleared_titles' => $clearedCount,
                'not_found_chapters' => $notFoundCount,
                'results' => $results,
                'message' => $message
            ];

        } catch (Exception $e) {
            file_put_contents('debug.log', "NovelManager: clearTranslatedTitles - Exception: " . $e->getMessage() . "\n", FILE_APPEND);
            return [
                'success' => false,
                'error' => 'Failed to clear translated titles: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Clear chapter translations (removes translated content while preserving original)
     */
    public function clearChapterTranslations(int $novelId, array $chapterNumbers): array {
        try {
            $clearedCount = 0;
            $notFoundCount = 0;
            $results = [];

            foreach ($chapterNumbers as $chapterNumber) {
                // Check if chapter exists
                $chapter = $this->db->fetchOne(
                    "SELECT id, translation_status FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                    [$novelId, $chapterNumber]
                );

                if (!$chapter) {
                    $results[] = [
                        'chapter_number' => $chapterNumber,
                        'success' => false,
                        'error' => 'Chapter not found'
                    ];
                    $notFoundCount++;
                    continue;
                }

                // Clear translation data
                $updateData = [
                    'translated_content' => null,
                    'translated_title' => null,
                    'translation_status' => 'pending',
                    'translation_date' => null,
                    'word_count' => 0
                ];

                $updated = $this->db->update('chapters', $updateData, 'id = ?', [$chapter['id']]);

                if ($updated > 0) {
                    $results[] = [
                        'chapter_number' => $chapterNumber,
                        'success' => true,
                        'previous_status' => $chapter['translation_status']
                    ];
                    $clearedCount++;
                } else {
                    $results[] = [
                        'chapter_number' => $chapterNumber,
                        'success' => false,
                        'error' => 'Failed to update chapter'
                    ];
                }
            }

            return [
                'success' => $notFoundCount === 0,
                'total_chapters' => count($chapterNumbers),
                'cleared_chapters' => $clearedCount,
                'not_found_chapters' => $notFoundCount,
                'results' => $results,
                'message' => $notFoundCount === 0
                    ? "All {$clearedCount} chapter translations cleared successfully"
                    : "{$clearedCount} translations cleared, {$notFoundCount} chapters not found"
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to clear translations: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Recrawl chapter content from source URL
     */
    public function recrawlChapter(int $novelId, int $chapterNumber): array {
        try {
            $this->db->beginTransaction();

            // Get chapter info
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if (!$chapter) {
                throw new Exception("Chapter not found");
            }

            if (!$chapter['chapter_url']) {
                throw new Exception("Chapter has no source URL to recrawl from");
            }

            // Get novel info for platform
            $novel = $this->db->fetchOne(
                "SELECT platform FROM novels WHERE id = ?",
                [$novelId]
            );

            if (!$novel) {
                throw new Exception("Novel not found");
            }

            if ($novel['platform'] === 'manual') {
                throw new Exception("Cannot recrawl manually entered chapters");
            }

            // Get appropriate crawler
            $crawler = $this->getCrawler($novel['platform']);
            if (!$crawler) {
                throw new Exception("No crawler available for platform: " . $novel['platform']);
            }

            // Fetch fresh content from source
            $chapterContent = $crawler->getChapterContent($chapter['chapter_url']);

            // Clear existing chunks if any
            $this->db->query("DELETE FROM chapter_chunks WHERE chapter_id = ?", [$chapter['id']]);

            // Clear existing WordPress posts for this chapter
            $this->db->query("DELETE FROM wordpress_posts WHERE chapter_id = ?", [$chapter['id']]);

            // Prepare update data
            $updateData = [
                'original_content' => $chapterContent['original_content'],
                'original_title' => $chapterContent['original_title'],
                'translated_content' => null, // Clear existing translation
                'translated_title' => null,
                'translation_status' => 'pending',
                'translation_date' => null,
                'word_count' => 0,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Handle furigana if detected
            if (isset($chapterContent['original_content_with_furigana'])) {
                $updateData['original_content_with_furigana'] = $chapterContent['original_content_with_furigana'];
                $updateData['furigana_count'] = $chapterContent['furigana_count'] ?? 0;
                $updateData['furigana_processing_status'] = 'completed';
            } else {
                $updateData['original_content_with_furigana'] = null;
                $updateData['furigana_count'] = 0;
                $updateData['furigana_processing_status'] = 'none';
            }

            // Update chapter
            $updated = $this->db->update('chapters', $updateData, 'id = ?', [$chapter['id']]);

            if ($updated === 0) {
                throw new Exception("Failed to update chapter with new content");
            }

            // Get content statistics
            $contentStats = $this->chapterChunker->calculateContentStats($chapterContent['original_content']);

            // Check if chapter needs chunking and create chunks if necessary
            $chunkingResult = null;
            if ($contentStats['needs_chunking']) {
                try {
                    $chunkingResult = $this->chapterChunker->splitChapter($chapter['id'], $updateData['original_content']);
                    if ($chunkingResult['success']) {
                        error_log("NovelManager: Recrawl chunking successful - created {$chunkingResult['chunks_created']} chunks");
                    }
                } catch (Exception $e) {
                    error_log("NovelManager: Recrawl chunking failed: " . $e->getMessage());
                    // Don't fail the recrawl operation if chunking fails
                    $chunkingResult = [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'chunks_created' => 0
                    ];
                }
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'Chapter recrawled successfully',
                'chapter' => array_merge($chapter, $chapterContent),
                'furigana_detected' => isset($updateData['original_content_with_furigana']),
                'furigana_count' => $updateData['furigana_count'] ?? 0,
                'content_stats' => $contentStats,
                'chunking_result' => $chunkingResult,
                'previous_content_length' => strlen($chapter['original_content'] ?? ''),
                'new_content_length' => strlen($chapterContent['original_content'])
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Clear chapter content while preserving the chapter entry and original title
     * This allows users to re-crawl/save the chapter later while keeping the original title intact
     */
    public function deleteChapter(int $novelId, int $chapterNumber): array {
        try {
            $this->db->beginTransaction();

            // Get chapter info
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if (!$chapter) {
                throw new Exception("Chapter not found");
            }

            // Check if chapter has any content to clear
            if (!$chapter['original_content'] && !$chapter['translated_content']) {
                throw new Exception("Chapter has no content to clear");
            }

            // Store original title for response (before clearing)
            $originalTitle = $chapter['original_title'];

            // Clear related data in correct order
            // 1. Delete chapter chunks
            $this->db->query("DELETE FROM chapter_chunks WHERE chapter_id = ?", [$chapter['id']]);

            // 2. Delete WordPress posts for this chapter
            $this->db->query("DELETE FROM wordpress_posts WHERE chapter_id = ?", [$chapter['id']]);

            // 3. Delete translation logs for this chapter
            $this->db->query("DELETE FROM translation_logs WHERE chapter_id = ?", [$chapter['id']]);

            // 4. Clear chapter content while preserving the record, URL, and original title
            $updateData = [
                'original_content' => null,
                // Preserve original_title - only clear translated content
                'translated_content' => null,
                'translated_title' => null,
                'original_content_with_furigana' => null,
                'word_count' => 0,
                'furigana_count' => 0,
                'translation_status' => 'pending',
                'furigana_processing_status' => 'none',
                'translation_date' => null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $updated = $this->db->update('chapters', $updateData, 'id = ?', [$chapter['id']]);

            if ($updated === 0) {
                throw new Exception("Failed to clear chapter content");
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'Chapter content cleared successfully',
                'cleared_chapter' => [
                    'id' => $chapter['id'],
                    'chapter_number' => $chapter['chapter_number'],
                    'original_title' => $originalTitle,
                    'chapter_url' => $chapter['chapter_url'] // Preserved for re-crawling
                ],
                'novel_id' => $novelId,
                'action_performed' => 'content_cleared'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
