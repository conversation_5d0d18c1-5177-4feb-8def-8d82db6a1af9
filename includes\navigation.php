<?php
/**
 * Navigation Component
 */

function renderNavigation($currentPage = 'dashboard') {
    $appName = defined('APP_NAME') ? APP_NAME : 'Novel Translator';
    ?>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-book-open me-2"></i>
                <?= htmlspecialchars($appName) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?= $currentPage === 'dashboard' ? 'active' : '' ?>" href="index.php">
                            <i class="fas fa-home me-1"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= $currentPage === 'novels' ? 'active' : '' ?>" href="novels.php">
                            <i class="fas fa-book me-1"></i>
                            My Novels
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= $currentPage === 'missing-content' ? 'active' : '' ?>" href="missing-content.php">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Missing Content
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?= in_array($currentPage, ['manual-entry', 'manual-chapter']) ? 'active' : '' ?>"
                           href="#" id="addNovelDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-plus me-1"></i>
                            Add Novel
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="index.php">
                                    <i class="fas fa-search me-2"></i>
                                    From URL (Auto-Crawl)
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="manual-entry.php">
                                    <i class="fas fa-edit me-2"></i>
                                    Manual Entry
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= $currentPage === 'settings' ? 'active' : '' ?>" href="settings.php">
                            <i class="fas fa-cog me-1"></i>
                            Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= $currentPage === 'help' ? 'active' : '' ?>" href="#help-section">
                            <i class="fas fa-question-circle me-1"></i>
                            Help
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <?php
}

// Auto-render navigation if this file is included directly
if (!function_exists('renderNavigation_called')) {
    renderNavigation();
    function renderNavigation_called() {}
}
?>
