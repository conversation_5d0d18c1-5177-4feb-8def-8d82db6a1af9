<?php
/**
 * Chapter Content Management API
 * Handles operations for managing chapter content, especially for chapters missing content
 */

require_once '../config/config.php';
require_once '../classes/Database.php';
require_once '../classes/NovelManager.php';
require_once '../includes/functions.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    $novelManager = new NovelManager();
} catch (Exception $e) {
    logError('Failed to initialize NovelManager: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);
    jsonResponse([
        'success' => false,
        'error' => 'Service initialization failed: ' . $e->getMessage()
    ], 500);
}

try {
    switch ($method) {
        case 'GET':
            handleGetMissingContent($novelManager);
            break;

        case 'PUT':
            handleUpdateChapterContent($novelManager);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    logError('Chapter Content API Exception: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);
    
    jsonResponse([
        'success' => false,
        'error' => 'An unexpected error occurred: ' . $e->getMessage()
    ], 500);
}

/**
 * Handle GET request - Get chapters missing content
 */
function handleGetMissingContent(NovelManager $novelManager): void {
    $novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : null;
    
    // Build options from query parameters
    $options = [];
    
    if (isset($_GET['platform']) && !empty($_GET['platform'])) {
        $options['platform'] = $_GET['platform'];
    }
    
    if (isset($_GET['translation_status']) && !empty($_GET['translation_status'])) {
        $options['translation_status'] = $_GET['translation_status'];
    }
    
    if (isset($_GET['order_by']) && !empty($_GET['order_by'])) {
        $options['order_by'] = $_GET['order_by'];
    }
    
    if (isset($_GET['limit']) && is_numeric($_GET['limit'])) {
        $options['limit'] = (int)$_GET['limit'];
    }
    
    $result = $novelManager->getChaptersMissingContent($novelId, $options);
    
    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result
        ]);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 400);
    }
}

/**
 * Handle PUT request - Update chapter content
 */
function handleUpdateChapterContent(NovelManager $novelManager): void {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }
    
    // Validate required fields
    $requiredFields = ['novel_id', 'chapter_number', 'original_content'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field])) {
            jsonResponse(['error' => "Missing required field: {$field}"], 400);
        }
    }
    
    $novelId = (int)$input['novel_id'];
    $chapterNumber = (int)$input['chapter_number'];
    
    if ($novelId <= 0) {
        jsonResponse(['error' => 'Invalid novel ID'], 400);
    }
    
    if ($chapterNumber <= 0) {
        jsonResponse(['error' => 'Invalid chapter number'], 400);
    }
    
    if (empty(trim($input['original_content']))) {
        jsonResponse(['error' => 'Original content cannot be empty'], 400);
    }
    
    // Prepare content data
    $contentData = [
        'original_content' => $input['original_content']
    ];
    
    // Add optional fields if provided
    if (isset($input['original_title']) && !empty(trim($input['original_title']))) {
        $contentData['original_title'] = $input['original_title'];
    }
    
    if (isset($input['translated_title']) && !empty(trim($input['translated_title']))) {
        $contentData['translated_title'] = $input['translated_title'];
    }
    
    if (isset($input['translated_content']) && !empty(trim($input['translated_content']))) {
        $contentData['translated_content'] = $input['translated_content'];
    }
    
    error_log("Chapter Content API: Updating content for novel {$novelId}, chapter {$chapterNumber}");
    
    $result = $novelManager->updateChapterContent($novelId, $chapterNumber, $contentData);
    
    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result
        ]);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 400);
    }
}
?>
