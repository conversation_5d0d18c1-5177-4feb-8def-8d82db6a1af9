/**
 * Missing Content Management JavaScript
 * Handles the interface for managing chapters missing content
 */

class MissingContentManager {
    constructor() {
        this.missingChapters = [];
        this.statistics = {};
        this.currentModal = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadMissingContent();
        this.loadNovelOptions();
    }
    
    bindEvents() {
        // Filter controls
        document.getElementById('apply-filters-btn').addEventListener('click', () => {
            this.loadMissingContent();
        });
        
        document.getElementById('clear-filters-btn').addEventListener('click', () => {
            this.clearFilters();
        });
        
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadMissingContent();
        });
        
        // Modal events
        document.getElementById('save-content-btn').addEventListener('click', () => {
            this.saveContent();
        });
        
        // Content textarea events
        document.getElementById('modal-original-content').addEventListener('input', () => {
            this.updateContentStats();
        });
        
        // Modal cleanup
        document.getElementById('contentModal').addEventListener('hidden.bs.modal', () => {
            this.resetModal();
        });
    }
    
    clearFilters() {
        document.getElementById('novel-filter').value = '';
        document.getElementById('platform-filter').value = '';
        document.getElementById('order-filter').value = 'novel_title';
        document.getElementById('limit-filter').value = '';
        this.loadMissingContent();
    }
    
    async loadNovelOptions() {
        try {
            const response = await fetch('api/novels.php');
            const data = await response.json();
            
            if (data.success && data.novels) {
                const novelSelect = document.getElementById('novel-filter');
                novelSelect.innerHTML = '<option value="">All Novels</option>';
                
                data.novels.forEach(novel => {
                    const title = novel.translated_title || novel.original_title;
                    const option = document.createElement('option');
                    option.value = novel.id;
                    option.textContent = `${title} (${novel.total_chapters} chapters)`;
                    novelSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading novel options:', error);
        }
    }
    
    async loadMissingContent() {
        const container = document.getElementById('missing-content-container');
        const statsContainer = document.getElementById('statistics-container');
        
        // Show loading state
        container.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Loading missing content...</p>
            </div>
        `;
        
        try {
            const params = this.buildFilterParams();
            const response = await fetch(`api/chapter-content.php?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.missingChapters = data.data.chapters;
                this.statistics = data.data.statistics;
                
                this.displayStatistics();
                this.displayMissingContent();
                
                statsContainer.style.display = 'block';
            } else {
                throw new Error(data.error || 'Failed to load missing content');
            }
        } catch (error) {
            console.error('Error loading missing content:', error);
            container.innerHTML = `
                <div class="text-center p-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <h5>Error Loading Content</h5>
                    <p>${error.message}</p>
                    <button class="btn btn-primary" onclick="missingContentManager.loadMissingContent()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Retry
                    </button>
                </div>
            `;
        }
    }
    
    buildFilterParams() {
        const params = new URLSearchParams();
        
        const novelId = document.getElementById('novel-filter').value;
        if (novelId) params.append('novel_id', novelId);
        
        const platform = document.getElementById('platform-filter').value;
        if (platform) params.append('platform', platform);
        
        const orderBy = document.getElementById('order-filter').value;
        if (orderBy) params.append('order_by', orderBy);
        
        const limit = document.getElementById('limit-filter').value;
        if (limit) params.append('limit', limit);
        
        return params.toString();
    }
    
    displayStatistics() {
        document.getElementById('total-missing').textContent = this.statistics.total_missing;
        document.getElementById('novels-affected').textContent = Object.keys(this.statistics.by_novel).length;
        document.getElementById('platforms-affected').textContent = Object.keys(this.statistics.by_platform).length;
    }
    
    displayMissingContent() {
        const container = document.getElementById('missing-content-container');
        
        if (this.missingChapters.length === 0) {
            container.innerHTML = `
                <div class="text-center p-4 text-success">
                    <i class="fas fa-check-circle fa-3x mb-3"></i>
                    <h5>No Missing Content Found</h5>
                    <p>All chapters have content! 🎉</p>
                </div>
            `;
            return;
        }
        
        const chaptersHtml = this.missingChapters.map(chapter => `
            <div class="chapter-missing-item border-bottom p-3">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <h6 class="mb-1">
                            <span class="badge bg-primary me-2">Ch. ${chapter.chapter_number}</span>
                            ${utils.escapeHtml(chapter.original_title || 'Untitled Chapter')}
                        </h6>
                        <small class="text-muted">
                            <i class="fas fa-book me-1"></i>
                            ${utils.escapeHtml(chapter.novel_title)}
                        </small>
                    </div>
                    <div class="col-md-3">
                        <span class="badge bg-secondary">${chapter.platform}</span>
                        <span class="badge bg-${this.getStatusColor(chapter.translation_status)} ms-1">
                            ${chapter.translation_status}
                        </span>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            ${new Date(chapter.chapter_created_at).toLocaleDateString()}
                        </small>
                    </div>
                    <div class="col-md-2 text-end">
                        <button class="btn btn-primary btn-sm" 
                                onclick="missingContentManager.openContentModal(${chapter.novel_id}, ${chapter.chapter_number})">
                            <i class="fas fa-plus me-1"></i>
                            Add Content
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = chaptersHtml;
    }
    
    getStatusColor(status) {
        switch (status) {
            case 'completed': return 'success';
            case 'translating': return 'warning';
            case 'error': return 'danger';
            default: return 'secondary';
        }
    }
    
    openContentModal(novelId, chapterNumber) {
        const chapter = this.missingChapters.find(c => 
            c.novel_id == novelId && c.chapter_number == chapterNumber
        );
        
        if (!chapter) {
            alert('Chapter not found');
            return;
        }
        
        // Populate modal
        document.getElementById('modal-novel-id').value = novelId;
        document.getElementById('modal-chapter-number').value = chapterNumber;
        document.getElementById('modal-novel-title').value = chapter.novel_title;
        document.getElementById('modal-chapter-title').value = `Chapter ${chapterNumber}`;
        document.getElementById('modal-original-title').value = chapter.original_title || '';
        document.getElementById('modal-translated-title').value = chapter.translated_title || '';
        document.getElementById('modal-original-content').value = '';
        document.getElementById('modal-translated-content').value = '';
        
        this.updateContentStats();
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('contentModal'));
        modal.show();
        this.currentModal = modal;
    }
    
    updateContentStats() {
        const content = document.getElementById('modal-original-content').value;
        const charCount = content.length;
        
        document.getElementById('modal-content-stats').textContent = `Character count: ${charCount}`;
        
        // Show chunking info if content is long
        if (charCount > 25000) {
            document.getElementById('modal-chunking-info').innerHTML = 
                '<span class="text-warning"><i class="fas fa-info-circle me-1"></i>This chapter will be automatically chunked for translation</span>';
        } else {
            document.getElementById('modal-chunking-info').textContent = '';
        }
    }
    
    async saveContent() {
        const saveBtn = document.getElementById('save-content-btn');
        const originalText = saveBtn.innerHTML;
        
        try {
            // Validate form
            const originalContent = document.getElementById('modal-original-content').value.trim();
            if (!originalContent) {
                alert('Original content is required');
                return;
            }
            
            // Show loading state
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
            
            // Prepare data
            const data = {
                novel_id: parseInt(document.getElementById('modal-novel-id').value),
                chapter_number: parseInt(document.getElementById('modal-chapter-number').value),
                original_content: originalContent
            };
            
            // Add optional fields
            const originalTitle = document.getElementById('modal-original-title').value.trim();
            if (originalTitle) data.original_title = originalTitle;
            
            const translatedTitle = document.getElementById('modal-translated-title').value.trim();
            if (translatedTitle) data.translated_title = translatedTitle;
            
            const translatedContent = document.getElementById('modal-translated-content').value.trim();
            if (translatedContent) data.translated_content = translatedContent;
            
            // Send request
            const response = await fetch('api/chapter-content.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Show success message
                utils.showAlert('Chapter content saved successfully!', 'success');
                
                // Close modal
                this.currentModal.hide();
                
                // Reload missing content
                this.loadMissingContent();
            } else {
                throw new Error(result.error || 'Failed to save content');
            }
            
        } catch (error) {
            console.error('Error saving content:', error);
            utils.showAlert('Error saving content: ' + error.message, 'danger');
        } finally {
            // Restore button
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        }
    }
    
    resetModal() {
        document.getElementById('content-form').reset();
        document.getElementById('modal-content-stats').textContent = 'Character count: 0';
        document.getElementById('modal-chunking-info').textContent = '';
        this.currentModal = null;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.missingContentManager = new MissingContentManager();
});
