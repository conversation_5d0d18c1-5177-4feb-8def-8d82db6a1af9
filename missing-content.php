<?php
/**
 * Missing Content Management Page
 * Shows chapters that are missing content and allows manual content addition
 */

require_once 'config/config.php';
require_once 'includes/header.php';

renderHeader('Missing Content Management');
?>

<?php 
function renderNavigation_called() {} // Prevent auto-render
include 'includes/navigation.php'; 
renderNavigation('missing-content');
?>

<div class="container mt-4">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
            Missing Content Management
        </h2>
        <div>
            <button id="refresh-btn" class="btn btn-outline-secondary">
                <i class="fas fa-sync-alt me-1"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="novel-filter" class="form-label">Novel</label>
                    <select class="form-select" id="novel-filter">
                        <option value="">All Novels</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="platform-filter" class="form-label">Platform</label>
                    <select class="form-select" id="platform-filter">
                        <option value="">All Platforms</option>
                        <option value="kakuyomu">Kakuyomu</option>
                        <option value="syosetu">Syosetu</option>
                        <option value="shuba69">Shuba69</option>
                        <option value="manual">Manual</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="order-filter" class="form-label">Order By</label>
                    <select class="form-select" id="order-filter">
                        <option value="novel_title">Novel Title</option>
                        <option value="chapter_number">Chapter Number</option>
                        <option value="created_date">Created Date</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="limit-filter" class="form-label">Limit</label>
                    <select class="form-select" id="limit-filter">
                        <option value="">No Limit</option>
                        <option value="50">50 chapters</option>
                        <option value="100">100 chapters</option>
                        <option value="200">200 chapters</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button id="apply-filters-btn" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        Apply Filters
                    </button>
                    <button id="clear-filters-btn" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-times me-1"></i>
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div id="statistics-container" class="mb-4" style="display: none;">
        <div class="row">
            <div class="col-md-4">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h3 class="mb-0" id="total-missing">0</h3>
                        <p class="mb-0">Total Missing</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-0" id="novels-affected">0</h3>
                        <p class="mb-0">Novels Affected</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-0" id="platforms-affected">0</h3>
                        <p class="mb-0">Platforms Affected</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Missing Content List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Chapters Missing Content
            </h5>
        </div>
        <div class="card-body p-0">
            <div id="missing-content-container">
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Loading missing content...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Addition Modal -->
<div class="modal fade" id="contentModal" tabindex="-1" aria-labelledby="contentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentModalLabel">
                    <i class="fas fa-edit me-2"></i>
                    Add Chapter Content
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="content-form">
                    <input type="hidden" id="modal-novel-id">
                    <input type="hidden" id="modal-chapter-number">
                    
                    <div class="mb-3">
                        <label for="modal-novel-title" class="form-label">Novel</label>
                        <input type="text" class="form-control" id="modal-novel-title" readonly>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="modal-chapter-title" class="form-label">Chapter Number</label>
                            <input type="text" class="form-control" id="modal-chapter-title" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="modal-original-title" class="form-label">Original Title</label>
                            <input type="text" class="form-control" id="modal-original-title" 
                                   placeholder="Enter or update chapter title">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-translated-title" class="form-label">Translated Title (Optional)</label>
                        <input type="text" class="form-control" id="modal-translated-title" 
                               placeholder="Enter translated chapter title">
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-original-content" class="form-label">
                            Original Content <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="modal-original-content" rows="15" 
                                  placeholder="Paste or type the original chapter content here..." required></textarea>
                        <div class="form-text">
                            <span id="modal-content-stats">Character count: 0</span>
                            <span class="ms-3" id="modal-chunking-info"></span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-translated-content" class="form-label">Translated Content (Optional)</label>
                        <textarea class="form-control" id="modal-translated-content" rows="10" 
                                  placeholder="Enter translated content if available"></textarea>
                        <div class="form-text">
                            If you provide translated content, the chapter will be marked as translated.
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="save-content-btn">
                    <i class="fas fa-save me-1"></i>
                    Save Content
                </button>
            </div>
        </div>
    </div>
</div>

<?php renderFooter(['assets/js/missing-content.js']); ?>
